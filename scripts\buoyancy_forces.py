"""
旋转浮力模块
提供计算物体浮力并根据物体姿态进行旋转变换的功能
用于水下机器人或浮体的三维浮力仿真，考虑物体的旋转姿态

重构版本：同时支持 ActionGraph 模式和纯 Python 调用
"""

import math
import numpy as np

# ==================== 纯 Python 接口 ====================

def calculate_rotated_buoyancy_force(volume, height, z_position, rotation_angles,
                                   water_density=1025.0, gravity=9.81):
    """
    计算考虑旋转姿态的浮力（纯 Python 版本）

    该函数不仅计算浮力大小，还考虑物体的旋转姿态，
    将垂直向上的浮力向量转换到物体的局部坐标系中

    参数:
        volume: 物体总体积 (m³)
        height: 物体高度 (m)
        z_position: 物体在Z轴的位置 (m)
        rotation_angles: 物体的旋转角度 [roll, pitch, yaw] (度)
        water_density: 水的密度 (kg/m³)，默认海水密度 1025
        gravity: 重力加速度 (m/s²)，默认 9.81

    返回:
        dict: 旋转浮力计算结果
            {
                'force_vector': [x, y, z],        # 旋转后的浮力向量 (N)
                'original_force': float,          # 原始浮力大小 (N)
                'submerged_volume': float,        # 浸没体积 (m³)
                'submerged_ratio': float,         # 浸没比例 (0.0-1.0)
                'rotation_matrix': numpy.matrix   # 使用的旋转矩阵
            }
    """
    # 计算浸没信息
    submerged_height = calculate_submerged_height(z_position, height)
    submerged_volume = volume * submerged_height / height  # 修正：使用比例
    submerged_ratio = submerged_height / height

    # 根据阿基米德原理计算浮力大小
    buoyancy_force = water_density * submerged_volume * gravity

    # 将角度转换为弧度
    rotation_radians = [math.radians(angle) for angle in rotation_angles]

    # 创建组合旋转矩阵
    rotation_matrix = create_combined_rotation_matrix(rotation_radians)

    # 创建浮力向量（原始浮力只在Z方向）并进行旋转变换
    buoyancy_vector = np.array([[0], [0], [buoyancy_force]])  # 垂直向上的浮力向量
    rotated_buoyancy_vector = rotation_matrix @ buoyancy_vector

    # 提取各方向分量
    force_vector = [
        -float(rotated_buoyancy_vector[0, 0]),  # X方向（取负值，适应坐标系）
        -float(rotated_buoyancy_vector[1, 0]),  # Y方向（取负值，适应坐标系）
        float(rotated_buoyancy_vector[2, 0])    # Z方向
    ]

    return {
        'force_vector': force_vector,
        'original_force': buoyancy_force,
        'submerged_volume': submerged_volume,
        'submerged_ratio': submerged_ratio,
        'rotation_matrix': rotation_matrix
    }

def create_combined_rotation_matrix(rotation_radians):
    """
    创建组合旋转矩阵（纯 Python 版本）

    按照 Roll-Pitch-Yaw 的顺序进行旋转变换

    参数:
        rotation_radians: 旋转角度 [roll, pitch, yaw] (弧度)

    返回:
        numpy.matrix: 3x3 组合旋转矩阵
    """
    roll, pitch, yaw = rotation_radians

    # 创建各轴的旋转矩阵
    roll_matrix = create_rotation_matrix_x(roll)     # 绕X轴旋转（横滚）
    pitch_matrix = create_rotation_matrix_y(pitch)   # 绕Y轴旋转（俯仰）
    yaw_matrix = create_rotation_matrix_z(yaw)       # 绕Z轴旋转（偏航）

    # 组合旋转矩阵：先横滚，再俯仰，最后偏航
    return roll_matrix @ pitch_matrix @ yaw_matrix

def calculate_buoyancy_torque(volume, height, z_position, rotation_angles,
                            center_of_buoyancy_offset=[0, 0, 0],
                            water_density=1025.0, gravity=9.81):
    """
    计算浮力产生的力矩（高级功能）

    参数:
        volume: 物体总体积 (m³)
        height: 物体高度 (m)
        z_position: 物体在Z轴的位置 (m)
        rotation_angles: 物体的旋转角度 [roll, pitch, yaw] (度)
        center_of_buoyancy_offset: 浮心相对于质心的偏移 [x, y, z] (m)
        water_density: 水的密度 (kg/m³)
        gravity: 重力加速度 (m/s²)

    返回:
        dict: 浮力力矩计算结果
            {
                'force_vector': [x, y, z],    # 浮力向量 (N)
                'torque_vector': [x, y, z],   # 力矩向量 (N·m)
                'center_of_buoyancy': [x, y, z] # 浮心位置 (m)
            }
    """
    # 计算旋转浮力
    buoyancy_result = calculate_rotated_buoyancy_force(
        volume, height, z_position, rotation_angles, water_density, gravity
    )

    # 计算浮心位置（考虑旋转）
    rotation_matrix = buoyancy_result['rotation_matrix']
    cob_offset_rotated = rotation_matrix @ np.array([[center_of_buoyancy_offset[0]],
                                                    [center_of_buoyancy_offset[1]],
                                                    [center_of_buoyancy_offset[2]]])

    center_of_buoyancy = [
        float(cob_offset_rotated[0, 0]),
        float(cob_offset_rotated[1, 0]),
        float(cob_offset_rotated[2, 0])
    ]

    # 计算力矩：τ = r × F
    force_vector = buoyancy_result['force_vector']
    torque_vector = np.cross(center_of_buoyancy, force_vector).tolist()

    return {
        'force_vector': force_vector,
        'torque_vector': torque_vector,
        'center_of_buoyancy': center_of_buoyancy
    }

# ==================== 旋转矩阵工具函数 ====================

def create_rotation_matrix_x(angle):
    """
    创建绕X轴旋转的旋转矩阵（横滚角 Roll）

    参数:
        angle: 绕X轴的旋转角度 (弧度)

    返回:
        numpy.matrix: 3x3的旋转矩阵
    """
    return np.matrix([
        [1, 0, 0],
        [0, math.cos(angle), -math.sin(angle)],
        [0, math.sin(angle), math.cos(angle)]
    ])

def create_rotation_matrix_y(angle):
    """
    创建绕Y轴旋转的旋转矩阵（俯仰角 Pitch）

    参数:
        angle: 绕Y轴的旋转角度 (弧度)

    返回:
        numpy.matrix: 3x3的旋转矩阵
    """
    return np.matrix([
        [math.cos(angle), 0, math.sin(angle)],
        [0, 1, 0],
        [-math.sin(angle), 0, math.cos(angle)]
    ])

def create_rotation_matrix_z(angle):
    """
    创建绕Z轴旋转的旋转矩阵（偏航角 Yaw）

    参数:
        angle: 绕Z轴的旋转角度 (弧度)

    返回:
        numpy.matrix: 3x3的旋转矩阵
    """
    return np.matrix([
        [math.cos(angle), -math.sin(angle), 0],
        [math.sin(angle), math.cos(angle), 0],
        [0, 0, 1]
    ])

def calculate_submerged_height(z_position, height):
    """
    根据物体的Z轴位置计算浸没高度比例（纯 Python 版本）

    参数:
        z_position: 物体中心的Z轴位置 (m)
        height: 物体的总高度 (m)

    返回:
        float: 浸没高度 (0.0 到 height)
    """
    center_of_h = height / 2

    if z_position >= center_of_h:
        return 0.0
    elif z_position < -center_of_h:
        return height
    else:
        return center_of_h - z_position

# ==================== ActionGraph 兼容接口 ====================

def setup(db):
    """
    初始化函数（ActionGraph 兼容）
    设置必要的变量或数据结构

    参数:
        db: 数据库对象，用于存储和传递数据

    注意:
        此模块不需要特殊的初始化操作
    """
    pass  # 此情况下不需要初始化操作

def compute(db):
    """
    计算浮力并根据物体姿态进行旋转变换（ActionGraph 兼容版本）

    参数:
        db: 数据库对象，包含输入和输出数据
            输入参数:
                - volume: 物体总体积 (m³)
                - height: 物体高度 (m)
                - z_position: 物体在Z轴的位置 (m)
                - rotation: 物体的旋转角度 [roll, pitch, yaw] (度)
            输出参数:
                - x_force: X方向的浮力分量 (N)
                - y_force: Y方向的浮力分量 (N)
                - z_force: Z方向的浮力分量 (N)
    """
    # 获取输入参数
    volume = db.inputs.volume
    height = db.inputs.height
    z_position = db.inputs.z_position
    rotation_angles = db.inputs.rotation  # 度数

    # 使用纯 Python 函数计算
    result = calculate_rotated_buoyancy_force(
        volume, height, z_position, rotation_angles
    )

    # 输出结果
    force_vector = result['force_vector']
    db.outputs.x_force = force_vector[0]
    db.outputs.y_force = force_vector[1]
    db.outputs.z_force = force_vector[2]

# ==================== 测试和演示函数 ====================

def test_rotated_buoyancy():
    """测试旋转浮力计算功能"""
    print("🧪 测试旋转浮力计算模块")
    print("="*40)

    # 测试参数
    volume = 8.0  # 2m x 2m x 2m 立方体
    height = 2.0
    z_position = -1.0  # 1米深度

    # 测试不同的旋转角度
    test_cases = [
        {'rotation': [0, 0, 0], 'desc': '无旋转'},
        {'rotation': [30, 0, 0], 'desc': '横滚30度'},
        {'rotation': [0, 45, 0], 'desc': '俯仰45度'},
        {'rotation': [0, 0, 90], 'desc': '偏航90度'},
        {'rotation': [15, 30, 45], 'desc': '复合旋转'}
    ]

    for i, case in enumerate(test_cases):
        print(f"\n{i+1}️⃣ {case['desc']} {case['rotation']}:")

        result = calculate_rotated_buoyancy_force(
            volume, height, z_position, case['rotation']
        )

        force = result['force_vector']
        print(f"   浮力向量: [{force[0]:.1f}, {force[1]:.1f}, {force[2]:.1f}] N")
        print(f"   原始浮力: {result['original_force']:.1f} N")
        print(f"   浸没比例: {result['submerged_ratio']:.2f}")

        # 计算总力大小
        total_force = math.sqrt(sum(f**2 for f in force))
        print(f"   总力大小: {total_force:.1f} N")

def test_buoyancy_torque():
    """测试浮力力矩计算功能"""
    print("\n🔄 测试浮力力矩计算")
    print("="*40)

    # 测试参数
    volume = 8.0
    height = 2.0
    z_position = -1.5
    rotation_angles = [10, 20, 30]  # 复合旋转
    center_of_buoyancy_offset = [0.1, 0.0, 0.2]  # 浮心偏移

    result = calculate_buoyancy_torque(
        volume, height, z_position, rotation_angles, center_of_buoyancy_offset
    )

    print(f"旋转角度: {rotation_angles} 度")
    print(f"浮心偏移: {center_of_buoyancy_offset} m")
    print(f"浮力向量: {[f'{f:.1f}' for f in result['force_vector']]} N")
    print(f"力矩向量: {[f'{t:.1f}' for t in result['torque_vector']]} N·m")
    print(f"浮心位置: {[f'{p:.2f}' for p in result['center_of_buoyancy']]} m")

if __name__ == "__main__":
    """当直接运行此文件时执行测试"""
    test_rotated_buoyancy()
    test_buoyancy_torque()
    print("\n✅ 旋转浮力模块测试完成！")
