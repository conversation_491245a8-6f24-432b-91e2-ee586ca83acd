# ROV 物理计算模块测试指南

## 🎯 概述

现在 `scripts/` 目录中的每个物理计算模块都包含了独立的测试函数和使用说明。您可以单独运行任何一个文件来查看该模块的功能演示和使用示例。

## 📁 可测试的模块列表

### 1. **基础浮力计算模块**
```bash
python scripts/buoyancy_control.py
```
**功能展示**：
- 不同尺寸 ROV 的浮力计算
- 不同深度位置的浸没状态
- 淡水 vs 海水密度影响
- 详细的使用示例和代码演示

### 2. **旋转浮力计算模块**
```bash
python scripts/buoyancy_forces.py
```
**功能展示**：
- 考虑 Roll-Pitch-Yaw 旋转的浮力计算
- 浮力力矩计算
- 不同旋转角度的影响对比
- 3D 旋转矩阵变换演示

### 3. **PID 控制器模块**
```bash
python scripts/controller.py
```
**功能展示**：
- PID 控制器的基础功能
- 深度控制和姿态控制
- 不同 PID 参数的效果对比
- 控制器状态演进过程

### 4. **阻尼计算模块**
```bash
python scripts/damping.py
```
**功能展示**：
- 不同深度的阻尼系数变化
- 线性 vs 二次阻力模型对比
- 环境阻尼综合计算
- 速度对阻力的影响

### 5. **推进器控制模块**
```bash
python scripts/linear_angular_control.py
```
**功能展示**：
- 四推进器配置的控制算法
- 操纵杆输入到推力分配
- 6自由度运动控制
- 不同运动模式的推力分布

### 6. **四元数转换模块**
```bash
python scripts/quat_to_euler.py
```
**功能展示**：
- 四元数到欧拉角的双向转换
- 多种四元数格式支持
- 自动格式检测和归一化
- 转换精度验证和万向锁避免

### 7. **统一物理引擎**
```bash
python scripts/rov_physics_unified.py
```
**功能展示**：
- 完整的 ROV 物理仿真
- 旋转浮力集成测试
- 四元数物理计算集成
- 便捷函数演示
- 多 ROV 配置对比

## 🧪 综合测试

### **完整测试套件**
```bash
python test_unified_physics.py
```
**包含内容**：
- 所有模块的集成测试
- 新旧计算方法对比
- 多 ROV 仿真演示
- 旋转浮力功能验证

## 📖 每个测试文件的内容结构

每个模块的测试都包含以下部分：

### **1. 功能说明**
- 模块的主要功能介绍
- 支持的计算类型
- ActionGraph 和纯 Python 双接口说明

### **2. 功能测试**
- 不同参数配置的测试
- 边界条件测试
- 性能和精度验证

### **3. 使用示例**
- 完整的代码示例
- 实际运行结果展示
- 常见使用场景演示

### **4. 相关模块链接**
- 指向其他相关模块
- 模块间的协作关系

## 🚀 快速开始

### **测试单个模块**
```bash
# 测试基础浮力计算
python scripts/buoyancy_control.py

# 测试旋转浮力计算
python scripts/buoyancy_forces.py

# 测试 PID 控制器
python scripts/controller.py

# 测试四元数转换
python scripts/quat_to_euler.py
```

### **测试所有模块**
```bash
# 运行完整测试套件
python test_unified_physics.py
```

### **在您的代码中使用**
```python
# 方式 1: 使用统一物理引擎（推荐）
from scripts.rov_physics_unified import create_rov_physics_engine
engine = create_rov_physics_engine(mass=1000, size=2.0, target_depth=-3.0)
result = engine.calculate_complete_physics(position, velocity, last_pos, dt)

# 方式 2: 使用独立模块
from scripts.buoyancy_forces import calculate_rotated_buoyancy_force
from scripts.controller import PIDController
from scripts.damping import calculate_environmental_damping
from scripts.quat_to_euler import quaternion_to_euler, euler_to_quaternion

# 方式 3: 使用便捷函数
from scripts.rov_physics_unified import calculate_simple_rov_physics
summary = calculate_simple_rov_physics(mass, size, target_depth, position, velocity, dt)
```

## 📊 测试输出示例

每个测试都会显示：
- 📖 **功能说明**：模块的作用和特点
- 🧪 **测试结果**：具体的数值输出
- 💡 **使用示例**：完整的代码示例
- ➡️ **实际输出**：示例代码的运行结果

## 🎉 优势

1. **✅ 独立测试**：每个模块都可以单独验证功能
2. **✅ 详细说明**：包含完整的使用说明和示例
3. **✅ 实时验证**：可以立即看到计算结果
4. **✅ 学习工具**：通过示例快速了解模块用法
5. **✅ 调试辅助**：便于排查问题和验证修改

现在您可以随时运行任何一个 `scripts/` 文件来查看其功能和获取使用说明！
