#!/usr/bin/env python3
"""
测试仅使用统一物理模块的 ROV 系统
验证去除简化计算后的功能
"""

from isaacsim import SimulationApp

simulation_app = SimulationApp({"headless": False})

import carb
import omni
import sys
import os
from isaacsim.core.api import SimulationContext

def test_unified_physics_import():
    """测试统一物理模块导入"""
    print("🧪 测试统一物理模块导入")
    
    try:
        # 尝试导入统一物理模块
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from scripts.rov_physics_unified import ROVPhysicsEngine, create_rov_physics_engine
        print("✅ 统一物理模块导入成功")
        
        # 测试创建物理引擎
        print("🔧 测试创建物理引擎...")
        physics_engine = create_rov_physics_engine(
            mass=20.0,
            size=1.0,
            target_depth=-1.5,
            max_thrust=150.0
        )
        print("✅ 物理引擎创建成功")
        
        # 测试物理计算
        print("🧮 测试物理计算...")
        position = [0.5, 0.5, -1.0]
        velocity = [0.0, 0.0, -0.1]
        last_position = [0.5, 0.5, -0.9]
        dt = 1.0/60.0
        
        result = physics_engine.calculate_complete_physics(
            position, velocity, last_position, dt
        )
        
        print("✅ 物理计算成功")
        print(f"📊 物理摘要: {result['physics_summary']}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 统一物理模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 物理引擎测试失败: {e}")
        return False

def test_calculate_rov_physics():
    """测试修改后的 calculate_rov_physics 函数"""
    print("\n🧪 测试 calculate_rov_physics 函数")
    
    try:
        # 导入修改后的模块
        import standalone_multi_rov_system as rov_system
        
        # 模拟 ROV 配置
        rov_config = {
            "name": "ROV_Test",
            "mass": 20.0,
            "size": 1.0,
            "target_depth": -1.5,
            "max_thrust": 150.0
        }
        
        # 模拟物理引擎初始化
        from scripts.rov_physics_unified import create_rov_physics_engine
        physics_engine = create_rov_physics_engine(
            rov_config["mass"],
            rov_config["size"],
            rov_config["target_depth"],
            rov_config["max_thrust"]
        )
        
        # 设置全局变量
        rov_system.rov_physics_engines = [physics_engine]
        rov_system.USE_UNIFIED_PHYSICS = True
        
        # 测试物理计算
        position = [0.5, 0.5, -1.0]
        last_position = [0.5, 0.5, -0.9]
        dt = 1.0/60.0
        
        result = rov_system.calculate_rov_physics(
            rov_config, position, last_position, dt, rov_index=0
        )
        
        print("✅ calculate_rov_physics 函数测试成功")
        print(f"📊 返回结果键: {list(result.keys())}")
        print(f"🔧 总力: {result['total_force']:.2f}N")
        print(f"🌊 浮力: {result['buoyancy_force']:.2f}N")
        print(f"🎯 控制力: {result['control_force']:.2f}N")
        print(f"💨 阻力: {result['drag_force']:.2f}N")
        print(f"📏 深度: {result['depth']:.2f}m")
        
        # 检查详细结果
        if 'detailed_result' in result:
            print("✅ 包含详细物理结果")
        if 'buoyancy_details' in result:
            print("✅ 包含浮力详细信息")
        if 'control_details' in result:
            print("✅ 包含控制详细信息")
        
        return True
        
    except Exception as e:
        print(f"❌ calculate_rov_physics 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 统一物理模块专用测试")
    print("=" * 60)
    
    # 初始化基础组件
    stage = simulation_app.context.get_stage()
    sim_context = SimulationContext(stage_units_in_meters=1.0)
    
    success_count = 0
    total_tests = 2
    
    # 测试1: 统一物理模块导入
    if test_unified_physics_import():
        success_count += 1
    
    # 测试2: calculate_rov_physics 函数
    if test_calculate_rov_physics():
        success_count += 1
    
    # 总结
    print(f"\n📊 测试总结: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("✅ 所有测试通过！统一物理模块工作正常")
        print("🎯 可以安全运行 standalone_multi_rov_system.py")
    else:
        print("❌ 部分测试失败，请检查统一物理模块")
    
    # 保持运行一段时间
    print("\n⏳ 保持运行 5 秒...")
    import time
    for i in range(5):
        simulation_app.update()
        time.sleep(1)
        print(f"  {i+1}/5")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ 测试错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("\n🔄 关闭 Isaac Sim...")
        simulation_app.close()
