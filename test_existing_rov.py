#!/usr/bin/env python3
"""
测试现有ROV查找功能
验证能否正确找到USD文件中已有的ROV
"""

import sys
import os

# 添加 scripts 目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
scripts_dir = os.path.join(current_dir, "scripts")
if scripts_dir not in sys.path:
    sys.path.append(scripts_dir)

def test_rov_search_logic():
    """测试ROV搜索逻辑"""
    print("🧪 测试ROV搜索逻辑")
    print("=" * 50)
    
    # 模拟不同的prim名称和路径
    test_cases = [
        ("ROV_01", "/World/ROV_01", True),
        ("rov_vehicle", "/World/Vehicles/rov_vehicle", True),
        ("robot_cube", "/World/Objects/robot_cube", True),
        ("underwater_box", "/World/underwater_box", True),
        ("Cube", "/World/Cube", True),
        ("random_object", "/World/random_object", False),
        ("light", "/World/Light", False),
        ("camera", "/World/Camera", False),
    ]
    
    rov_keywords = ['rov', 'robot', 'vehicle', 'cube', 'box']
    
    print("🔍 测试关键词匹配:")
    for name, path, expected in test_cases:
        name_lower = name.lower()
        path_lower = path.lower()
        
        # 检查是否匹配ROV关键词
        matches = any(keyword in name_lower or keyword in path_lower for keyword in rov_keywords)
        
        status = "✅" if matches == expected else "❌"
        print(f"   {status} {name} ({path}) -> 匹配: {matches}")

def analyze_usd_structure():
    """分析USD文件可能的结构"""
    print("\n🧪 分析USD文件可能的结构")
    print("=" * 50)
    
    print("📁 常见的USD场景结构:")
    print("   /World")
    print("   ├── /World/ROV (最可能)")
    print("   ├── /World/Cube (如果ROV是立方体)")
    print("   ├── /World/Vehicle")
    print("   ├── /World/Objects/ROV")
    print("   ├── /World/Vehicles/ROV_01")
    print("   └── /World/Environment")
    print("")
    
    print("🎯 搜索策略:")
    print("1. 首先尝试配置中指定的路径")
    print("2. 如果找不到，遍历整个场景")
    print("3. 查找包含ROV关键词的prim")
    print("4. 验证prim是否有几何体或变换属性")
    print("5. 返回第一个匹配的prim")

def test_position_extraction():
    """测试位置提取逻辑"""
    print("\n🧪 测试位置提取逻辑")
    print("=" * 50)
    
    print("📍 位置获取策略:")
    print("1. 使用 get_prim_position() 函数")
    print("2. 如果失败，使用配置中的默认位置")
    print("3. 显示ROV的初始位置信息")
    print("")
    
    print("🔧 预期行为:")
    print("• 如果ROV在水面上 (z > 0): 显示实际位置")
    print("• 如果ROV在水下 (z < 0): 显示实际深度")
    print("• 如果无法获取位置: 使用目标深度 -1.5m")

def simulate_rov_finding():
    """模拟ROV查找过程"""
    print("\n🧪 模拟ROV查找过程")
    print("=" * 50)
    
    # 模拟ROV配置
    rov_config = {
        "name": "Test_ROV",
        "path": "/World/ROV",
        "size": 1.0,
        "mass": 20.0,
        "target_depth": -1.5,
        "color": (0.2, 0.6, 1.0)
    }
    
    print(f"🎯 查找ROV: {rov_config['name']}")
    print(f"   配置路径: {rov_config['path']}")
    print(f"   目标深度: {rov_config['target_depth']}m")
    print("")
    
    print("🔍 查找步骤:")
    print("1. 尝试直接路径查找...")
    print("   stage.GetPrimAtPath('/World/ROV')")
    print("")
    
    print("2. 如果失败，搜索整个场景...")
    print("   for prim in stage.Traverse():")
    print("       检查prim名称和路径中的关键词")
    print("       验证prim类型和属性")
    print("")
    
    print("3. 返回结果:")
    print("   ✅ 找到现有ROV -> 使用现有的")
    print("   ❌ 未找到 -> 创建新的")

def main():
    """主测试函数"""
    print("🧪 现有ROV查找功能测试")
    print("验证能否正确找到USD文件中的ROV")
    print("")
    
    # 运行所有测试
    test_rov_search_logic()
    analyze_usd_structure()
    test_position_extraction()
    simulate_rov_finding()
    
    print("\n" + "=" * 50)
    print("✅ 测试完成")
    print("=" * 50)
    print("🎯 修改总结:")
    print("• 添加了 find_existing_rov() 函数")
    print("• 优先使用USD文件中已有的ROV")
    print("• 智能搜索ROV相关的prim")
    print("• 获取ROV的实际初始位置")
    print("• 只在找不到时才创建新ROV")
    print("")
    print("🚀 现在运行仿真时:")
    print("• 会首先搜索现有的ROV")
    print("• 显示找到的ROV信息")
    print("• 使用ROV的当前位置作为起点")
    print("• 不会创建重复的ROV")
    print("")
    print("📝 注意事项:")
    print("• 确保您的USD文件中的ROV有合适的名称")
    print("• ROV应该包含几何体或变换属性")
    print("• 如果ROV名称特殊，可能需要调整搜索关键词")

if __name__ == "__main__":
    main()
