#!/usr/bin/env python3
"""
测试 ROV 创建功能
简化版本，专门测试 xform 操作问题
"""

from isaacsim import SimulationApp

simulation_app = SimulationApp({"headless": False})

import carb
import omni
from isaacsim.core.api import SimulationContext
from pxr import UsdGeom, Gf, UsdPhysics, Usd

def test_rov_creation():
    """测试 ROV 创建"""
    print("🧪 测试 ROV 创建功能")
    
    # 初始化
    stage = simulation_app.context.get_stage()
    sim_context = SimulationContext(stage_units_in_meters=1.0)
    
    # ROV 配置
    rov_config = {
        "name": "ROV_Test",
        "path": "/World/ROV_Test",
        "mass": 20.0,
        "size": 1.0,
        "target_depth": -1.5,
        "color": (0.8, 0.2, 0.2),
    }
    
    print(f"🔧 创建 {rov_config['name']} 在 {rov_config['path']}")
    
    try:
        # 方法1：使用 UsdGeom.Cube.Define
        print("📝 方法1：使用 UsdGeom.Cube.Define")
        cube_geom = UsdGeom.Cube.Define(stage, rov_config["path"])
        cube_geom.CreateSizeAttr(rov_config["size"])
        
        # 检查现有的 xform 操作
        xformable = UsdGeom.Xformable(cube_geom)
        existing_ops = xformable.GetOrderedXformOps()
        print(f"  现有 xform 操作数量: {len(existing_ops)}")
        
        for i, op in enumerate(existing_ops):
            print(f"    {i}: {op.GetOpName()} ({op.GetOpType()})")
        
        # 安全地设置位置
        translate_ops = [op for op in existing_ops if op.GetOpType() == UsdGeom.XformOp.TypeTranslate]
        
        if translate_ops:
            print("  🔄 使用现有的 translate 操作")
            translate_ops[0].Set(Gf.Vec3f(0.5, 0.5, rov_config["target_depth"]))
        else:
            print("  🆕 创建新的 translate 操作")
            translate_op = cube_geom.AddTranslateOp()
            translate_op.Set(Gf.Vec3f(0.5, 0.5, rov_config["target_depth"]))
        
        # 设置颜色
        cube_geom.CreateDisplayColorAttr([rov_config["color"]])
        
        # 获取 prim
        prim = cube_geom.GetPrim()
        
        # 添加物理属性
        print("  🔧 添加物理属性")
        UsdPhysics.RigidBodyAPI.Apply(prim)
        UsdPhysics.CollisionAPI.Apply(prim)
        mass_api = UsdPhysics.MassAPI.Apply(prim)
        mass_api.CreateMassAttr(rov_config["mass"])
        
        print(f"  ✅ {rov_config['name']} 创建成功")
        
        # 测试位置获取
        print("📍 测试位置获取")
        
        # 方法1：XformCommonAPI
        try:
            xform_api = UsdGeom.XformCommonAPI(prim)
            translation, _, _ = xform_api.GetTranslate()
            print(f"  XformCommonAPI: {translation}")
        except Exception as e:
            print(f"  XformCommonAPI 失败: {e}")
        
        # 方法2：直接属性
        try:
            xform_attr = prim.GetAttribute("xformOp:translate")
            if xform_attr:
                position = xform_attr.Get()
                print(f"  直接属性: {position}")
            else:
                print("  直接属性: 未找到")
        except Exception as e:
            print(f"  直接属性失败: {e}")
        
        # 方法3：Xformable
        try:
            xformable = UsdGeom.Xformable(prim)
            ops = xformable.GetOrderedXformOps()
            for op in ops:
                if op.GetOpType() == UsdGeom.XformOp.TypeTranslate:
                    position = op.Get()
                    print(f"  Xformable: {position}")
                    break
        except Exception as e:
            print(f"  Xformable 失败: {e}")
        
        print("✅ ROV 创建测试完成")
        return True
        
    except Exception as e:
        print(f"❌ ROV 创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🧪 ROV 创建测试")
    print("=" * 50)
    
    try:
        success = test_rov_creation()
        if success:
            print("\n✅ 测试成功")
            print("🔄 保持运行状态，请检查 Isaac Sim 窗口")
            
            # 保持运行
            import time
            for i in range(10):
                simulation_app.update()
                time.sleep(1)
                print(f"⏳ 运行中... {i+1}/10")
        else:
            print("\n❌ 测试失败")
            
    except Exception as e:
        print(f"❌ 测试错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("\n🔄 关闭 Isaac Sim...")
        simulation_app.close()

if __name__ == "__main__":
    main()
