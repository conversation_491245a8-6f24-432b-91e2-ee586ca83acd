"""
浮力控制模块
提供计算物体浮力的功能，用于水下机器人或浮体的浮力仿真

重构版本：同时支持 ActionGraph 模式和纯 Python 调用
"""

# ==================== 纯 Python 接口 ====================

def calculate_buoyancy_force(volume, height, z_position, water_density=1025.0, gravity=9.81):
    """
    计算物体的浮力（纯 Python 版本）

    根据阿基米德原理计算浮力：F = ρ * V_submerged * g
    其中 ρ 是流体密度，V_submerged 是浸没体积，g 是重力加速度

    参数:
        volume: 物体总体积 (m³)
        height: 物体高度 (m)
        z_position: 物体在Z轴的位置 (m)
        water_density: 水的密度 (kg/m³)，默认海水密度 1025
        gravity: 重力加速度 (m/s²)，默认 9.81

    返回:
        dict: 包含浮力计算结果的字典
            {
                'buoyancy_force': float,      # Z方向浮力 (N)
                'submerged_volume': float,    # 浸没体积 (m³)
                'submerged_ratio': float,     # 浸没比例 (0.0-1.0)
                'depth': float               # 浸没深度 (m)
            }
    """
    # 计算浸没信息
    submerged_info = calculate_submerged_info(z_position, height)

    # 计算浸没体积
    submerged_volume = volume * submerged_info['submerged_ratio']

    # 根据阿基米德原理计算浮力
    buoyancy_force = water_density * submerged_volume * gravity

    return {
        'buoyancy_force': buoyancy_force,
        'submerged_volume': submerged_volume,
        'submerged_ratio': submerged_info['submerged_ratio'],
        'depth': submerged_info['depth']
    }
#  TODO 添加关于不同形状物体的浮力计算，暂时只有方形浮力计算
def calculate_submerged_info(z_position, height):
    """
    根据物体的Z轴位置计算浸没信息（纯 Python 版本）

    假设水面位于Z=0处，物体中心在z_position位置

    参数:
        z_position: 物体中心的Z轴位置 (m)
                   正值表示在水面上方，负值表示在水面下方
        height: 物体的总高度 (m)

    返回:
        dict: 浸没信息字典
            {
                'submerged_ratio': float,    # 浸没比例 (0.0-1.0)
                'submerged_height': float,   # 浸没高度 (m)
                'depth': float              # 物体中心深度 (m)
            }
    """
    center_of_h = height / 2  # 物体中心到顶部/底部的距离
    depth = max(0.0, -z_position)  # 物体中心深度（正值）

    if z_position >= center_of_h:
        # 物体完全在水面上方
        submerged_ratio = 0.0
        submerged_height = 0.0
    elif z_position <= -center_of_h:
        # 物体完全在水面下方
        submerged_ratio = 1.0
        submerged_height = height
    else:
        # 物体部分浸没
        submerged_height = center_of_h - z_position
        submerged_ratio = submerged_height / height

    return {
        'submerged_ratio': submerged_ratio,
        'submerged_height': submerged_height,
        'depth': depth
    }

# ==================== ActionGraph 兼容接口 ====================

def setup(db):
    """
    初始化函数（ActionGraph 兼容）
    设置必要的变量或数据结构

    参数:
        db: 数据库对象，用于存储和传递数据

    注意:
        此模块不需要特殊的初始化操作
    """
    pass  # 此情况下不需要初始化操作

def compute(db):
    """
    计算物体的浮力（ActionGraph 兼容版本）

    参数:
        db: 数据库对象，包含输入和输出数据
            输入参数:
                - Volume: 物体总体积 (m³)
                - height: 物体高度 (m)
                - z_position: 物体在Z轴的位置 (m)
            输出参数:
                - z_force: 计算得到的Z方向浮力 (N)
    """
    # 获取输入参数
    volume = db.inputs.Volume
    height = db.inputs.height
    z_position = db.inputs.z_position

    # 使用纯 Python 函数计算
    result = calculate_buoyancy_force(volume, height, z_position)

    # 将计算结果输出到数据库
    db.outputs.z_force = result['buoyancy_force']

# ==================== 向后兼容 ====================

def calculate_submerged_height(z_position, height):
    """
    向后兼容函数：计算浸没高度比例

    注意：此函数保留用于向后兼容，建议使用 calculate_submerged_info
    """
    info = calculate_submerged_info(z_position, height)
    return info['submerged_height']

# ==================== 测试和演示函数 ====================

def test_buoyancy_control():
    """测试基础浮力控制模块"""
    print("🧪 测试基础浮力控制模块 (buoyancy_control.py)")
    print("="*50)

    print("📖 模块功能说明:")
    print("   - 基于阿基米德原理计算浮力：F = ρ * V_submerged * g")
    print("   - 支持部分浸没和完全浸没的情况")
    print("   - 提供详细的浸没信息（体积、比例、深度）")
    print("   - 同时支持 ActionGraph 和纯 Python 调用")

    # 测试不同的物体配置
    test_objects = [
        {'name': '小型ROV', 'volume': 1.0, 'height': 1.0},
        {'name': '中型ROV', 'volume': 8.0, 'height': 2.0},
        {'name': '大型ROV', 'volume': 27.0, 'height': 3.0}
    ]

    # 测试不同的深度位置
    test_positions = [
        {'z': 1.0, 'desc': '完全在水面上方'},
        {'z': 0.0, 'desc': '中心在水面'},
        {'z': -0.5, 'desc': '部分浸没'},
        {'z': -2.0, 'desc': '完全浸没'}
    ]

    # 显示测试物体信息
    print(f"\n📋 测试物体信息:")
    print("   " + "="*50)
    for i, obj in enumerate(test_objects, 1):
        max_buoyancy_fresh = obj['volume'] * 1000 * 9.81  # 淡水中最大浮力
        max_buoyancy_sea = obj['volume'] * 1025 * 9.81    # 海水中最大浮力

        print(f"   {i}. {obj['name']}:")
        print(f"      体积: {obj['volume']:.1f} m³")
        print(f"      高度: {obj['height']:.1f} m")
        print(f"      淡水最大浮力: {max_buoyancy_fresh:.1f} N")
        print(f"      海水最大浮力: {max_buoyancy_sea:.1f} N")
        print(f"      浮力差异: {max_buoyancy_sea - max_buoyancy_fresh:.1f} N")
        if i < len(test_objects):
            print()

    print(f"\n📍 测试深度位置:")
    for i, pos in enumerate(test_positions, 1):
        print(f"   {i}. Z = {pos['z']:+.1f}m - {pos['desc']}")

    print(f"\n1️⃣ 浮力计算测试:")

    for obj in test_objects:
        print(f"\n🤖 {obj['name']} (体积={obj['volume']}m³, 高度={obj['height']}m):")

        for pos in test_positions:
            result = calculate_buoyancy_force(
                volume=obj['volume'],
                height=obj['height'],
                z_position=pos['z']
            )

            print(f"   {pos['desc']} (z={pos['z']}m):")
            print(f"     浮力: {result['buoyancy_force']:.1f} N")
            print(f"     浸没比例: {result['submerged_ratio']:.2f}")
            print(f"     浸没体积: {result['submerged_volume']:.2f} m³")
            print(f"     深度: {result['depth']:.2f} m")

def demo_usage_examples():
    """演示使用示例"""
    print(f"\n📚 使用示例:")
    print("="*30)

    print("\n💡 示例 1: 基础浮力计算")
    print("```python")
    print("from scripts.buoyancy_control import calculate_buoyancy_force")
    print("")
    print("# 计算 2x2x2m 立方体在 1m 深度的浮力")
    print("result = calculate_buoyancy_force(")
    print("    volume=8.0,      # 8立方米")
    print("    height=2.0,      # 2米高")
    print("    z_position=-1.0  # 1米深度")
    print(")")
    print("print(f'浮力: {result[\"buoyancy_force\"]:.1f} N')")
    print("```")

    # 实际运行示例
    result = calculate_buoyancy_force(volume=8.0, height=2.0, z_position=-1.0)
    print(f"➡️ 输出: 浮力: {result['buoyancy_force']:.1f} N")

    print("\n💡 示例 2: 浸没信息查询")
    print("```python")
    print("from scripts.buoyancy_control import calculate_submerged_info")
    print("")
    print("# 查询物体的浸没状态")
    print("info = calculate_submerged_info(z_position=-0.5, height=2.0)")
    print("print(f'浸没比例: {info[\"submerged_ratio\"]:.2f}')")
    print("```")

    # 实际运行示例
    info = calculate_submerged_info(z_position=-0.5, height=2.0)
    print(f"➡️ 输出: 浸没比例: {info['submerged_ratio']:.2f}")

    print("\n💡 示例 3: 不同水密度的影响")
    print("```python")
    print("# 淡水 vs 海水浮力对比")
    print("fresh_water = calculate_buoyancy_force(8.0, 2.0, -1.0, water_density=1000)")
    print("sea_water = calculate_buoyancy_force(8.0, 2.0, -1.0, water_density=1025)")
    print("```")

    # 实际运行示例
    fresh_water = calculate_buoyancy_force(8.0, 2.0, -1.0, water_density=1000)
    sea_water = calculate_buoyancy_force(8.0, 2.0, -1.0, water_density=1025)
    print(f"➡️ 淡水浮力: {fresh_water['buoyancy_force']:.1f} N")
    print(f"➡️ 海水浮力: {sea_water['buoyancy_force']:.1f} N")
    print(f"➡️ 差异: {sea_water['buoyancy_force'] - fresh_water['buoyancy_force']:.1f} N")

if __name__ == "__main__":
    """当直接运行此文件时执行测试和演示"""
    test_buoyancy_control()
    demo_usage_examples()
    print("\n✅ 基础浮力控制模块测试完成！")
    print("\n🔗 相关模块:")
    print("   - buoyancy_forces.py: 旋转浮力计算")
    print("   - rov_physics_unified.py: 统一物理引擎")
