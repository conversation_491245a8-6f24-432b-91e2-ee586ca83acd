"""
ROV 统一物理计算模块
整合浮力、控制、阻尼和推进器控制功能
为 standalone 模式提供完整的物理计算接口

这个模块将原本分散在多个 ActionGraph 脚本中的功能
重构为可以直接在 Python 中调用的统一接口
"""

import sys
import os

# 添加 scripts 目录到路径，以便导入其他模块
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 导入重构后的模块
from buoyancy_control import calculate_buoyancy_force, calculate_submerged_info
from buoyancy_forces import calculate_rotated_buoyancy_force, calculate_buoyancy_torque
from controller import PIDController, calculate_depth_control_force, calculate_attitude_control_force
from damping import calculate_damping_coefficients, calculate_drag_force, calculate_environmental_damping
from linear_angular_control import ThrusterController, calculate_thruster_allocation, calculate_6dof_thruster_forces
from quat_to_euler import quaternion_to_euler, euler_to_quaternion, validate_quaternion


class ROVPhysicsEngine:
    """
    ROV 物理引擎类
    
    整合所有物理计算功能，提供统一的接口
    """
    
    def __init__(self, rov_config):
        """
        初始化 ROV 物理引擎
        
        参数:
            rov_config: ROV 配置字典
                {
                    'mass': float,           # 质量 (kg)
                    'volume': float,         # 体积 (m³)
                    'size': float,           # 尺寸 (m)
                    'target_depth': float,   # 目标深度 (m)
                    'max_thrust': float      # 最大推力 (N)
                }
        """
        self.config = rov_config
        
        # 计算体积（如果未提供）
        if 'volume' not in rov_config:
            size = rov_config['size']
            self.config['volume'] = size ** 3  # 立方体体积
        
        # 初始化控制器
        self.depth_controller = PIDController(
            kp=self._get_adaptive_gain(), 
            ki=10.0, 
            kd=0.01
        )
        self.attitude_controller = PIDController(kp=100.0, ki=10.0, kd=0.01)
        self.thruster_controller = ThrusterController(
            max_thrust=rov_config.get('max_thrust', 1000.0)
        )
        
        # 物理常数
        self.water_density = 1025.0  # 海水密度 kg/m³
        self.gravity = 9.81          # 重力加速度 m/s²
        self.max_damping = 50.0      # 最大阻尼系数
    
    def _get_adaptive_gain(self):
        """根据 ROV 质量计算自适应控制增益"""
        mass = self.config['mass']
        if mass < 600:
            return 300.0
        elif mass < 900:
            return 500.0
        else:
            return 700.0
    
    def calculate_rotated_buoyancy_physics(self, position, rotation_angles):
        """
        计算考虑旋转姿态的浮力

        参数:
            position: 当前位置 [x, y, z]
            rotation_angles: 旋转角度 [roll, pitch, yaw] (度)

        返回:
            dict: 旋转浮力计算结果
        """
        return calculate_rotated_buoyancy_force(
            volume=self.config['volume'],
            height=self.config['size'],
            z_position=position[2],
            rotation_angles=rotation_angles,
            water_density=self.water_density,
            gravity=self.gravity
        )

    def calculate_buoyancy_torque_physics(self, position, rotation_angles, center_of_buoyancy_offset=[0, 0, 0]):
        """
        计算浮力产生的力矩

        参数:
            position: 当前位置 [x, y, z]
            rotation_angles: 旋转角度 [roll, pitch, yaw] (度)
            center_of_buoyancy_offset: 浮心相对于质心的偏移 [x, y, z] (m)

        返回:
            dict: 浮力力矩计算结果
        """
        return calculate_buoyancy_torque(
            volume=self.config['volume'],
            height=self.config['size'],
            z_position=position[2],
            rotation_angles=rotation_angles,
            center_of_buoyancy_offset=center_of_buoyancy_offset,
            water_density=self.water_density,
            gravity=self.gravity
        )

    def calculate_quaternion_physics(self, quaternion, position):
        """
        基于四元数计算旋转物理效应

        参数:
            quaternion: 四元数 [x, y, z, w] 或 [w, x, y, z]
            position: 当前位置 [x, y, z]

        返回:
            dict: 四元数物理计算结果
        """
        # 验证四元数
        validation = validate_quaternion(quaternion)
        if not validation['is_valid']:
            raise ValueError(f"无效的四元数: {validation['message']}")

        # 转换四元数为欧拉角
        euler_result = quaternion_to_euler(quaternion, degrees=True)
        rotation_angles = euler_result['euler_angles']

        # 计算旋转浮力
        buoyancy_result = self.calculate_rotated_buoyancy_physics(position, rotation_angles)

        # 计算浮力力矩
        torque_result = self.calculate_buoyancy_torque_physics(position, rotation_angles)

        return {
            'quaternion': quaternion,
            'euler_angles': rotation_angles,
            'quaternion_format': euler_result['input_format'],
            'quaternion_validation': validation,
            'rotated_buoyancy': buoyancy_result,
            'buoyancy_torque': torque_result,
            'physics_summary': {
                'quaternion_norm': validation['norm'],
                'is_unit_quaternion': validation['is_unit'],
                'roll': rotation_angles[0],
                'pitch': rotation_angles[1],
                'yaw': rotation_angles[2],
                'buoyancy_force_magnitude': sum([f**2 for f in buoyancy_result['force_vector']])**0.5,
                'torque_magnitude': sum([t**2 for t in torque_result['torque_vector']])**0.5
            }
        }

    def convert_euler_to_quaternion_physics(self, roll, pitch, yaw, degrees=True):
        """
        将欧拉角转换为四元数并计算相关物理效应

        参数:
            roll: 横滚角
            pitch: 俯仰角
            yaw: 偏航角
            degrees: 输入是否为度数

        返回:
            dict: 欧拉角到四元数转换结果
        """
        # 转换欧拉角为四元数
        quat_result = euler_to_quaternion(roll, pitch, yaw, degrees)

        return {
            'input_euler': [roll, pitch, yaw],
            'quaternion_xyzw': quat_result['quaternion'],
            'quaternion_wxyz': quat_result['quaternion_wxyz'],
            'quaternion_norm': quat_result['norm'],
            'conversion_info': {
                'input_format': 'degrees' if degrees else 'radians',
                'output_formats': {
                    'xyzw': quat_result['quaternion'],
                    'wxyz': quat_result['quaternion_wxyz']
                }
            }
        }

    def calculate_complete_physics(self, position, velocity, last_position, dt, joystick_input=None):
        """
        计算完整的 ROV 物理状态
        
        参数:
            position: 当前位置 [x, y, z] (m)
            velocity: 当前速度 [vx, vy, vz] (m/s)
            last_position: 上一帧位置 [x, y, z] (m)
            dt: 时间步长 (s)
            joystick_input: 操纵杆输入 {'x': float, 'y': float}，可选
            
        返回:
            dict: 完整的物理计算结果
        """
        z_pos = position[2]
        height = self.config['size']
        volume = self.config['volume']
        target_depth = self.config['target_depth']
        
        # 1. 浮力计算
        buoyancy_result = calculate_buoyancy_force(
            volume, height, z_pos, self.water_density, self.gravity
        )
        
        # 2. 深度控制力计算
        control_result = calculate_depth_control_force(
            z_pos, target_depth, self.depth_controller
        )
        
        # 3. 阻尼计算
        damping_result = calculate_environmental_damping(
            z_pos, height, velocity, self.max_damping
        )
        
        # 4. 推进器控制（如果有操纵杆输入）
        thruster_result = None
        if joystick_input:
            thruster_result = calculate_thruster_allocation(
                joystick_input.get('y', 0.0),
                joystick_input.get('x', 0.0),
                self.thruster_controller.max_thrust
            )
        
        # 5. 合力计算
        drag_force_z = damping_result['drag_force'][2] if isinstance(damping_result['drag_force'], list) else damping_result['drag_force']
        total_force_z = (
            buoyancy_result['buoyancy_force'] +
            control_result['control_force'] +
            drag_force_z
        )
        
        return {
            'buoyancy': buoyancy_result,
            'control': control_result,
            'damping': damping_result,
            'thrusters': thruster_result,
            'total_force': {
                'x': 0.0,
                'y': 0.0,
                'z': total_force_z
            },
            'physics_summary': {
                'depth': buoyancy_result['depth'],
                'buoyancy_force': buoyancy_result['buoyancy_force'],
                'control_force': control_result['control_force'],
                'drag_force': drag_force_z,
                'total_force_z': total_force_z
            }
        }

# ==================== 便捷函数 ====================

def create_rov_physics_engine(mass, size, target_depth, max_thrust=1000.0):
    """
    创建 ROV 物理引擎的便捷函数
    
    参数:
        mass: ROV 质量 (kg)
        size: ROV 尺寸 (m)
        target_depth: 目标深度 (m)
        max_thrust: 最大推力 (N)
        
    返回:
        ROVPhysicsEngine: 配置好的物理引擎实例
    """
    rov_config = {
        'mass': mass,
        'size': size,
        'volume': size ** 3,
        'target_depth': target_depth,
        'max_thrust': max_thrust
    }
    return ROVPhysicsEngine(rov_config)

def calculate_simple_rov_physics(mass, size, target_depth, current_position, velocity, dt):
    """
    简化的 ROV 物理计算函数
    
    参数:
        mass: ROV 质量 (kg)
        size: ROV 尺寸 (m)
        target_depth: 目标深度 (m)
        current_position: 当前位置 [x, y, z] (m)
        velocity: 当前速度 [vx, vy, vz] (m/s)
        dt: 时间步长 (s)
        
    返回:
        dict: 简化的物理计算结果
    """
    # 创建临时物理引擎
    engine = create_rov_physics_engine(mass, size, target_depth)
    
    # 计算物理状态
    last_position = [
        current_position[0] - velocity[0] * dt,
        current_position[1] - velocity[1] * dt,
        current_position[2] - velocity[2] * dt
    ]
    
    result = engine.calculate_complete_physics(
        current_position, velocity, last_position, dt
    )
    
    return result['physics_summary']

def calculate_rov_rotated_buoyancy(mass, size, position, rotation_angles,
                                 water_density=1025.0, gravity=9.81):
    """
    计算 ROV 旋转浮力的便捷函数

    参数:
        mass: ROV 质量 (kg)
        size: ROV 尺寸 (m)
        position: 当前位置 [x, y, z]
        rotation_angles: 旋转角度 [roll, pitch, yaw] (度)
        water_density: 水密度 (kg/m³)
        gravity: 重力加速度 (m/s²)

    返回:
        dict: 旋转浮力计算结果
    """
    volume = size ** 3  # 立方体体积

    return calculate_rotated_buoyancy_force(
        volume=volume,
        height=size,
        z_position=position[2],
        rotation_angles=rotation_angles,
        water_density=water_density,
        gravity=gravity
    )

def calculate_rov_buoyancy_torque(mass, size, position, rotation_angles,
                                center_of_buoyancy_offset=[0, 0, 0],
                                water_density=1025.0, gravity=9.81):
    """
    计算 ROV 浮力力矩的便捷函数

    参数:
        mass: ROV 质量 (kg)
        size: ROV 尺寸 (m)
        position: 当前位置 [x, y, z]
        rotation_angles: 旋转角度 [roll, pitch, yaw] (度)
        center_of_buoyancy_offset: 浮心偏移 [x, y, z] (m)
        water_density: 水密度 (kg/m³)
        gravity: 重力加速度 (m/s²)

    返回:
        dict: 浮力力矩计算结果
    """
    volume = size ** 3  # 立方体体积

    return calculate_buoyancy_torque(
        volume=volume,
        height=size,
        z_position=position[2],
        rotation_angles=rotation_angles,
        center_of_buoyancy_offset=center_of_buoyancy_offset,
        water_density=water_density,
        gravity=gravity
    )

def calculate_rov_quaternion_physics(mass, size, quaternion, position,
                                   water_density=1025.0, gravity=9.81):
    """
    基于四元数计算 ROV 物理效应的便捷函数

    参数:
        mass: ROV 质量 (kg)
        size: ROV 尺寸 (m)，假设为立方体
        quaternion: 四元数 [x, y, z, w] 或 [w, x, y, z]
        position: 当前位置 [x, y, z] (m)
        water_density: 水密度 (kg/m³)
        gravity: 重力加速度 (m/s²)

    返回:
        dict: 四元数物理计算结果
    """
    # 创建临时物理引擎
    engine = create_rov_physics_engine(mass, size, target_depth=-1.0)
    engine.water_density = water_density
    engine.gravity = gravity

    # 计算四元数物理效应
    return engine.calculate_quaternion_physics(quaternion, position)

def convert_rov_euler_to_quaternion(roll, pitch, yaw, degrees=True):
    """
    ROV 欧拉角到四元数转换的便捷函数

    参数:
        roll: 横滚角
        pitch: 俯仰角
        yaw: 偏航角
        degrees: 输入是否为度数

    返回:
        dict: 转换结果
    """
    return euler_to_quaternion(roll, pitch, yaw, degrees)

def convert_rov_quaternion_to_euler(quaternion, degrees=True):
    """
    ROV 四元数到欧拉角转换的便捷函数

    参数:
        quaternion: 四元数 [x, y, z, w] 或 [w, x, y, z]
        degrees: 是否返回度数

    返回:
        dict: 转换结果
    """
    return quaternion_to_euler(quaternion, degrees)

def validate_rov_quaternion(quaternion):
    """
    验证 ROV 四元数的便捷函数

    参数:
        quaternion: 四元数 [x, y, z, w] 或 [w, x, y, z]

    返回:
        dict: 验证结果
    """
    return validate_quaternion(quaternion)

# ==================== 测试函数 ====================

def test_unified_physics():
    """测试统一物理计算模块"""
    print("🧪 测试 ROV 统一物理计算模块 (rov_physics_unified.py)")
    print("="*60)

    print("📖 模块功能说明:")
    print("   - 整合所有物理计算功能的统一接口")
    print("   - 支持基础浮力和旋转浮力计算")
    print("   - 集成 PID 控制、阻尼计算、推进器控制")
    print("   - 提供完整的 ROV 物理仿真解决方案")
    print("   - 支持多种便捷调用方式")

    # 测试配置
    test_configs = [
        {'mass': 800, 'size': 1.8, 'target_depth': -1.5, 'name': 'ROV_Original'},
        {'mass': 1000, 'size': 2.0, 'target_depth': -4.0, 'name': 'ROV_Main'},
        {'mass': 500, 'size': 1.6, 'target_depth': -2.0, 'name': 'ROV_Scout'}
    ]

    # 显示测试物体信息
    print(f"\n📋 测试物体信息:")
    print("   " + "="*50)
    for i, config in enumerate(test_configs, 1):
        volume = config['size'] ** 3  # 立方体体积
        density = config['mass'] / volume  # 密度
        max_buoyancy = volume * 1025 * 9.81  # 最大浮力（海水中完全浸没）

        print(f"   {i}. {config['name']}:")
        print(f"      质量: {config['mass']} kg")
        print(f"      尺寸: {config['size']}m × {config['size']}m × {config['size']}m")
        print(f"      体积: {volume:.2f} m³")
        print(f"      密度: {density:.1f} kg/m³")
        print(f"      目标深度: {config['target_depth']} m")
        print(f"      最大浮力: {max_buoyancy:.1f} N")
        print(f"      浮力-重力差: {max_buoyancy - config['mass']*9.81:.1f} N")
        if i < len(test_configs):
            print()

    print(f"\n1️⃣ 测试统一物理引擎:")
    for config in test_configs:
        print(f"\n🤖 测试 {config['name']}:")

        # 创建物理引擎
        engine = create_rov_physics_engine(
            config['mass'], config['size'], config['target_depth']
        )

        # 测试位置
        test_position = [0, 0, -1.0]  # 1米深度
        test_velocity = [0, 0, -0.1]  # 下沉速度
        test_last_pos = [0, 0, -0.9]

        # 计算物理状态
        result = engine.calculate_complete_physics(
            test_position, test_velocity, test_last_pos, 1/60
        )

        summary = result['physics_summary']
        print(f"     深度: {summary['depth']:.2f}m")
        print(f"     浮力: {summary['buoyancy_force']:.1f}N")
        print(f"     控制力: {summary['control_force']:.1f}N")
        print(f"     总力: {summary['total_force_z']:.1f}N")

def test_rotated_buoyancy_integration():
    """测试旋转浮力集成"""
    print(f"\n2️⃣ 测试旋转浮力集成:")

    # 测试物体参数
    test_mass = 1000  # kg
    test_size = 2.0   # m
    test_depth = -2.0 # m

    print(f"   📋 测试物体信息:")
    print(f"      质量: {test_mass} kg")
    print(f"      尺寸: {test_size}m × {test_size}m × {test_size}m")
    print(f"      体积: {test_size**3:.1f} m³")
    print(f"      测试深度: {abs(test_depth)} m")
    print(f"      标准浮力: {test_size**3 * 1025 * 9.81:.1f} N")

    # 创建测试引擎
    engine = create_rov_physics_engine(mass=test_mass, size=test_size, target_depth=-3.0)

    # 测试不同旋转角度
    test_rotations = [
        {'angles': [0, 0, 0], 'desc': '无旋转'},
        {'angles': [30, 0, 0], 'desc': '横滚30度'},
        {'angles': [0, 45, 0], 'desc': '俯仰45度'},
        {'angles': [15, 30, 45], 'desc': '复合旋转'}
    ]

    position = [0, 0, test_depth]  # 使用定义的测试深度

    for test in test_rotations:
        result = engine.calculate_rotated_buoyancy_physics(position, test['angles'])
        force = result['force_vector']

        print(f"   {test['desc']} {test['angles']}:")
        print(f"     浮力向量: [{force[0]:.1f}, {force[1]:.1f}, {force[2]:.1f}] N")
        print(f"     原始浮力: {result['original_force']:.1f} N")

def test_convenience_functions():
    """测试便捷函数"""
    print(f"\n3️⃣ 测试便捷函数:")

    # 便捷函数测试参数
    test_mass = 1000
    test_size = 2.0
    test_target_depth = -3.0
    test_position = [0, 0, -1.5]
    test_velocity = [0, 0, -0.2]

    print(f"   📋 便捷函数测试参数:")
    print(f"      ROV质量: {test_mass} kg")
    print(f"      ROV尺寸: {test_size}m³")
    print(f"      目标深度: {abs(test_target_depth)} m")
    print(f"      当前位置: {test_position}")
    print(f"      当前速度: {test_velocity} m/s")

    # 测试简化物理计算
    print("\n   简化物理计算:")
    result = calculate_simple_rov_physics(
        mass=test_mass, size=test_size, target_depth=test_target_depth,
        current_position=test_position,
        velocity=test_velocity,
        dt=1/60
    )

    print(f"     深度: {result['depth']:.2f}m")
    print(f"     浮力: {result['buoyancy_force']:.1f}N")
    print(f"     控制力: {result['control_force']:.1f}N")
    print(f"     总力: {result['total_force_z']:.1f}N")

    # 测试旋转浮力便捷函数
    print("\n   旋转测力便捷函数:")
    test_rotation = [20, 30, 40]
    buoyancy_result = calculate_rov_rotated_buoyancy(
        mass=test_mass, size=test_size,
        position=test_position,
        rotation_angles=test_rotation
    )
    print(f"     旋转角度: {test_rotation}°")

    force = buoyancy_result['force_vector']
    print(f"     旋转浮力: [{force[0]:.1f}, {force[1]:.1f}, {force[2]:.1f}] N")
    print(f"     浸没比例: {buoyancy_result['submerged_ratio']:.2f}")

def test_quaternion_physics_integration():
    """测试四元数物理集成"""
    print(f"\n4️⃣ 测试四元数物理集成:")

    # 四元数测试参数
    quat_test_mass = 1000
    quat_test_size = 2.0
    quat_test_position = [0, 0, -2.0]

    print(f"   📋 四元数测试物体信息:")
    print(f"      质量: {quat_test_mass} kg")
    print(f"      尺寸: {quat_test_size}m³")
    print(f"      测试位置: {quat_test_position}")
    print(f"      测试深度: {abs(quat_test_position[2])} m")

    # 创建测试引擎
    engine = create_rov_physics_engine(mass=quat_test_mass, size=quat_test_size, target_depth=-3.0)

    # 测试不同四元数
    test_quaternions = [
        {'quat': [0, 0, 0, 1], 'desc': '无旋转'},
        {'quat': [0.7071, 0, 0, 0.7071], 'desc': '绕X轴90度'},
        {'quat': [0, 0, 0.7071, 0.7071], 'desc': '绕Z轴90度'},
        {'quat': [0.1830, 0.3536, 0.1830, 0.9007], 'desc': '复合旋转'}
    ]

    for test in test_quaternions:
        try:
            result = engine.calculate_quaternion_physics(test['quat'], quat_test_position)
            summary = result['physics_summary']

            print(f"   {test['desc']}:")
            print(f"     四元数: {test['quat']}")
            print(f"     欧拉角: [{summary['roll']:.1f}°, {summary['pitch']:.1f}°, {summary['yaw']:.1f}°]")
            print(f"     浮力大小: {summary['buoyancy_force_magnitude']:.1f} N")
            print(f"     力矩大小: {summary['torque_magnitude']:.1f} N·m")
            print(f"     四元数模长: {summary['quaternion_norm']:.4f}")
        except Exception as e:
            print(f"   {test['desc']}: 计算失败 - {str(e)}")

def test_quaternion_convenience_functions():
    """测试四元数便捷函数"""
    print(f"\n5️⃣ 测试四元数便捷函数:")

    # 便捷函数测试参数
    conv_test_mass = 1000
    conv_test_size = 2.0
    conv_test_position = [0, 0, -2.0]
    test_euler = [30, 45, 60]
    test_quat = [0, 0, 0.7071, 0.7071]

    print(f"   📋 便捷函数测试参数:")
    print(f"      测试欧拉角: {test_euler}°")
    print(f"      测试四元数: {test_quat}")
    print(f"      物理计算质量: {conv_test_mass} kg")
    print(f"      物理计算尺寸: {conv_test_size}m³")

    # 测试欧拉角到四元数转换
    print("\n   欧拉角到四元数转换:")
    euler_result = convert_rov_euler_to_quaternion(test_euler[0], test_euler[1], test_euler[2])
    quat = euler_result['quaternion']
    print(f"     输入: {test_euler}°")
    print(f"     输出: [{quat[0]:.4f}, {quat[1]:.4f}, {quat[2]:.4f}, {quat[3]:.4f}]")

    # 测试四元数到欧拉角转换
    print("\n   四元数到欧拉角转换:")
    quat_result = convert_rov_quaternion_to_euler(test_quat)
    euler = quat_result['euler_angles']
    print(f"     输入: {test_quat}")
    print(f"     输出: [{euler[0]:.1f}°, {euler[1]:.1f}°, {euler[2]:.1f}°]")

    # 测试四元数验证
    print("\n   四元数验证:")
    validation = validate_rov_quaternion([0, 0, 0, 1])
    print(f"     输入: [0, 0, 0, 1]")
    print(f"     验证: {validation['message']}")

    # 测试四元数物理计算便捷函数
    print("\n   四元数物理计算:")
    physics_result = calculate_rov_quaternion_physics(
        mass=conv_test_mass, size=conv_test_size,
        quaternion=test_quat,
        position=conv_test_position
    )
    summary = physics_result['physics_summary']
    print(f"     浮力大小: {summary['buoyancy_force_magnitude']:.1f} N")
    print(f"     力矩大小: {summary['torque_magnitude']:.1f} N·m")

def demo_usage_examples():
    """演示使用示例"""
    print(f"\n📚 使用示例:")
    print("="*30)

    print("\n💡 示例 1: 创建统一物理引擎")
    print("```python")
    print("from scripts.rov_physics_unified import create_rov_physics_engine")
    print("")
    print("# 创建 ROV 物理引擎")
    print("engine = create_rov_physics_engine(")
    print("    mass=1000.0,        # 1000kg ROV")
    print("    size=2.0,           # 2m x 2m x 2m")
    print("    target_depth=-3.0   # 目标深度 3m")
    print(")")
    print("")
    print("# 计算完整物理状态")
    print("result = engine.calculate_complete_physics(")
    print("    position=[0, 0, -1.5],")
    print("    velocity=[0, 0, -0.1],")
    print("    last_position=[0, 0, -1.4],")
    print("    dt=1/60")
    print(")")
    print("```")

    print("\n💡 示例 2: 使用便捷函数")
    print("```python")
    print("from scripts.rov_physics_unified import calculate_simple_rov_physics")
    print("")
    print("# 一次性计算所有物理量")
    print("summary = calculate_simple_rov_physics(")
    print("    mass=800, size=1.8, target_depth=-2.0,")
    print("    current_position=[0, 0, -1.0],")
    print("    velocity=[0, 0, -0.15],")
    print("    dt=1/60")
    print(")")
    print("print(f'深度: {summary[\"depth\"]:.2f}m')")
    print("```")

    print("\n💡 示例 3: 旋转浮力计算")
    print("```python")
    print("from scripts.rov_physics_unified import calculate_rov_rotated_buoyancy")
    print("")
    print("# 计算考虑旋转的浮力")
    print("buoyancy = calculate_rov_rotated_buoyancy(")
    print("    mass=1000, size=2.0,")
    print("    position=[0, 0, -2.0],")
    print("    rotation_angles=[15, 30, 45]  # Roll, Pitch, Yaw")
    print(")")
    print("```")

    print("\n💡 示例 4: 四元数物理计算")
    print("```python")
    print("from scripts.rov_physics_unified import calculate_rov_quaternion_physics")
    print("")
    print("# 基于四元数计算物理效应")
    print("result = calculate_rov_quaternion_physics(")
    print("    mass=1000, size=2.0,")
    print("    quaternion=[0, 0, 0.7071, 0.7071],  # 绕Z轴90度")
    print("    position=[0, 0, -2.0]")
    print(")")
    print("print(f'欧拉角: {result[\"physics_summary\"][\"roll\"]:.1f}°, '")
    print("      f'{result[\"physics_summary\"][\"pitch\"]:.1f}°, '")
    print("      f'{result[\"physics_summary\"][\"yaw\"]:.1f}°')")
    print("```")

    print("\n💡 示例 5: 四元数转换")
    print("```python")
    print("from scripts.rov_physics_unified import convert_rov_euler_to_quaternion")
    print("")
    print("# 欧拉角转四元数")
    print("quat_result = convert_rov_euler_to_quaternion(30, 45, 60)")
    print("print(f'四元数: {quat_result[\"quaternion\"]}')")
    print("```")

if __name__ == "__main__":
    """当直接运行此文件时执行测试和演示"""
    test_unified_physics()
    test_rotated_buoyancy_integration()
    test_convenience_functions()
    test_quaternion_physics_integration()
    test_quaternion_convenience_functions()
    demo_usage_examples()
    print("\n✅ ROV 统一物理引擎测试完成！")
    print("\n🌟 这是所有物理计算模块的集成中心")
    print("🔗 包含的子模块:")
    print("   - buoyancy_control.py: 基础浮力")
    print("   - buoyancy_forces.py: 旋转浮力")
    print("   - controller.py: PID 控制")
    print("   - damping.py: 阻尼计算")
    print("   - linear_angular_control.py: 推进器控制")
    print("   - quat_to_euler.py: 四元数转换")
