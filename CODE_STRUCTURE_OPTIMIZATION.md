# standalone_multi_rov_system.py 代码结构优化说明

## 问题分析

### 原始代码结构问题

1. **全局代码执行**：
   - `load_usd_environment()` 在第131行被直接调用，不在任何函数内
   - 这违反了 Isaac Sim standalone 模式的最佳实践

2. **全局变量初始化**：
   - `stage = simulation_app.context.get_stage()`
   - `sim_context = SimulationContext(stage_units_in_meters=1.0)`
   - `physx_interface = omni.physx.acquire_physx_interface()`
   - 这些初始化代码在模块级别执行，不符合官方推荐模式

3. **不符合 Isaac Sim 官方模式**：
   - 官方推荐：SimulationApp 创建后，所有初始化应在 main() 函数中进行
   - 原代码：在模块导入时就执行了大量初始化操作

## 优化后的代码结构

### 1. 模块级别（导入和声明）
```python
from isaacsim import SimulationApp

# 创建 SimulationApp（必须在其他 omni 导入之前）
simulation_app = SimulationApp({"headless": False})

# 其他导入
import carb
import omni
# ... 其他导入

# 全局变量声明（不初始化）
stage = None
sim_context = None
physx_interface = None
```

### 2. 初始化函数
```python
def initialize_isaac_sim():
    """初始化 Isaac Sim 核心组件"""
    global stage, sim_context, physx_interface
    
    # 在这里进行所有核心组件的初始化
    stage = simulation_app.context.get_stage()
    sim_context = SimulationContext(stage_units_in_meters=1.0)
    physx_interface = omni.physx.acquire_physx_interface()
```

### 3. 主函数结构
```python
def main():
    """主函数 - 独立模式入口"""
    # 步骤1: 初始化核心组件
    initialize_isaac_sim()
    
    # 步骤2: 加载环境
    load_usd_environment()
    
    # 步骤3: 运行仿真
    run_multi_rov_simulation()
```

### 4. 程序入口
```python
if __name__ == "__main__":
    try:
        main()
    finally:
        simulation_app.close()
```

## 优化的好处

### 1. 符合官方最佳实践
- 遵循 Isaac Sim standalone 模式的官方推荐结构
- SimulationApp 创建后，所有初始化在 main() 中进行

### 2. 更好的错误处理
- 每个初始化步骤都有独立的错误处理
- 可以精确定位初始化失败的环节

### 3. 更清晰的执行流程
- 明确的步骤顺序：SimulationApp → 核心组件 → 环境加载 → 仿真运行
- 每个步骤都有清晰的日志输出

### 4. 更好的代码维护性
- 模块化的初始化函数
- 全局变量的生命周期管理更清晰
- 便于调试和扩展

## Isaac Sim Standalone 模式最佳实践

### 官方推荐的代码结构：
```python
from isaacsim import SimulationApp
simulation_app = SimulationApp({"headless": False})

# 在 SimulationApp 创建后导入其他 omni 模块
import carb
import omni
from isaacsim.core.api import SimulationContext

def main():
    # 所有初始化逻辑都在 main() 函数中
    sim_context = SimulationContext()
    physx_interface = omni.physx.acquire_physx_interface()
    
    # 业务逻辑
    # ...
    
    # 更新循环
    while True:
        simulation_app.update()

if __name__ == "__main__":
    try:
        main()
    finally:
        simulation_app.close()
```

### 为什么这样做？

1. **避免导入时副作用**：模块导入时不应该执行复杂的初始化操作
2. **确保正确的初始化顺序**：SimulationApp 必须在其他 omni 组件之前创建
3. **更好的资源管理**：在 finally 块中确保 simulation_app.close() 被调用
4. **便于测试和调试**：所有初始化逻辑集中在 main() 函数中

## 总结

优化后的代码结构：
- ✅ 符合 Isaac Sim 官方最佳实践
- ✅ 避免了模块级别的副作用
- ✅ 提供了更好的错误处理和日志
- ✅ 代码结构更清晰，便于维护和扩展
- ✅ 全局变量的生命周期管理更合理
