#!/usr/bin/env python3
"""
测试3D力向量修复
验证X、Y、Z轴力的正确处理
"""

import sys
import os

# 添加 scripts 目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
scripts_dir = os.path.join(current_dir, "scripts")
if scripts_dir not in sys.path:
    sys.path.append(scripts_dir)

def test_unified_physics_3d_forces():
    """测试统一物理模块的3D力计算"""
    print("🧪 测试统一物理模块3D力计算")
    print("=" * 50)
    
    try:
        from rov_physics_unified import create_rov_physics_engine
        
        # 创建物理引擎
        engine = create_rov_physics_engine(
            mass=20.0,
            size=1.0,
            target_depth=-1.5,
            max_thrust=150.0
        )
        
        # 测试位置和速度
        position = [0.5, 0.5, -1.0]
        velocity = [0.0, 0.0, -0.1]
        last_position = [0.5, 0.5, -0.9]
        dt = 1.0/60.0
        
        print("🔧 测试不同的操纵杆输入:")
        
        # 测试用例
        test_cases = [
            (None, "无输入"),
            ({'x': 0.0, 'y': 0.3}, "向前推进"),
            ({'x': 0.3, 'y': 0.0}, "向右推进"),
            ({'x': 0.0, 'y': -0.3}, "向后推进"),
            ({'x': -0.3, 'y': 0.0}, "向左推进"),
            ({'x': 0.2, 'y': 0.2}, "斜向推进"),
        ]
        
        for joystick_input, description in test_cases:
            print(f"\n📊 {description}:")
            if joystick_input:
                print(f"   输入: x={joystick_input.get('x', 0):.1f}, y={joystick_input.get('y', 0):.1f}")
            
            # 计算物理状态
            result = engine.calculate_complete_physics(
                position, velocity, last_position, dt, joystick_input
            )
            
            # 显示结果
            total_force = result['total_force']
            summary = result['physics_summary']
            
            print(f"   总力: [{total_force['x']:.1f}, {total_force['y']:.1f}, {total_force['z']:.1f}] N")
            print(f"   深度: {summary['depth']:.2f}m")
            print(f"   浮力: {summary['buoyancy_force']:.1f}N")
            print(f"   控制力: {summary['control_force']:.1f}N")
            
            # 检查推进器结果
            if result.get('thrusters'):
                thrusters = result['thrusters']
                print(f"   推进器: {thrusters}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_force_application_logic():
    """测试力应用逻辑"""
    print("\n🧪 测试力应用逻辑")
    print("=" * 50)
    
    # 模拟ROV配置
    rov_config = {
        'mass': 20.0,
        'name': 'Test_ROV'
    }
    
    # 测试不同的力向量
    test_forces = [
        ([0.0, 0.0, -100.0], "纯Z轴力（下沉）"),
        ([50.0, 0.0, 0.0], "纯X轴力（向右）"),
        ([0.0, 50.0, 0.0], "纯Y轴力（向前）"),
        ([10.0, 20.0, -30.0], "3D复合力"),
    ]
    
    # 物理参数
    dt = 1.0 / 60.0
    damping_factor = 0.95
    max_velocity = 2.0
    
    # 初始状态
    current_pos = [0.5, 0.5, -1.0]
    current_velocity = [0.0, 0.0, 0.0]
    
    for force_vector, description in test_forces:
        print(f"\n🔧 {description}: {force_vector}")
        
        # 计算加速度
        acceleration = [f / rov_config['mass'] for f in force_vector]
        
        # 更新速度
        new_velocity = [
            (current_velocity[i] + acceleration[i] * dt) * damping_factor
            for i in range(3)
        ]
        
        # 限制速度
        for i in range(3):
            if abs(new_velocity[i]) > max_velocity:
                new_velocity[i] = max_velocity if new_velocity[i] > 0 else -max_velocity
        
        # 计算位移
        displacement = [new_velocity[i] * dt for i in range(3)]
        new_position = [current_pos[i] + displacement[i] for i in range(3)]
        
        print(f"   加速度: [{acceleration[0]:.3f}, {acceleration[1]:.3f}, {acceleration[2]:.3f}] m/s²")
        print(f"   新速度: [{new_velocity[0]:.3f}, {new_velocity[1]:.3f}, {new_velocity[2]:.3f}] m/s")
        print(f"   位移: [{displacement[0]:.4f}, {displacement[1]:.4f}, {displacement[2]:.4f}] m")
        print(f"   新位置: [{new_position[0]:.3f}, {new_position[1]:.3f}, {new_position[2]:.3f}] m")

def analyze_coordinate_system():
    """分析坐标系统"""
    print("\n🧪 坐标系统分析")
    print("=" * 50)
    
    print("📐 Isaac Sim 坐标系统:")
    print("   X轴: 正值向右 (East)")
    print("   Y轴: 正值向前 (North)")
    print("   Z轴: 正值向上 (Up)")
    print("")
    
    print("🎮 操纵杆映射:")
    print("   joystick.x > 0: 向右移动 (+X)")
    print("   joystick.x < 0: 向左移动 (-X)")
    print("   joystick.y > 0: 向前移动 (+Y)")
    print("   joystick.y < 0: 向后移动 (-Y)")
    print("")
    
    print("🌊 水下物理:")
    print("   水面: Z = 0")
    print("   水下: Z < 0")
    print("   浮力: +Z方向")
    print("   重力: -Z方向")
    print("   推进器: 可产生X、Y、Z方向的力")

def main():
    """主测试函数"""
    print("🧪 3D力向量修复测试")
    print("验证X、Y、Z轴力的正确处理")
    print("")
    
    success_count = 0
    total_tests = 1
    
    # 测试统一物理模块
    if test_unified_physics_3d_forces():
        success_count += 1
        print("✅ 统一物理模块3D力测试通过")
    else:
        print("❌ 统一物理模块3D力测试失败")
    
    # 测试力应用逻辑
    test_force_application_logic()
    print("✅ 力应用逻辑测试完成")
    
    # 分析坐标系统
    analyze_coordinate_system()
    
    print("\n" + "=" * 50)
    print("📊 测试总结")
    print("=" * 50)
    print(f"✅ 通过测试: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎯 修复总结:")
        print("• 统一物理模块现在支持3D力向量")
        print("• X、Y轴推进器力正确计算")
        print("• 力应用函数支持3D向量")
        print("• 添加了速度跟踪和阻尼")
        print("• 限制最大速度防止不稳定")
        print("")
        print("🚀 现在可以测试修复后的仿真:")
        print("   python standalone_multi_rov_system.py")
        print("")
        print("🎮 预期行为:")
        print("• 5秒后开始自动测试不同方向的推进")
        print("• 每3秒切换一个方向: 前→右→后→左")
        print("• 输出显示3D力向量 [x,y,z]")
        print("• ROV应该在水平面内移动")
    else:
        print("❌ 部分测试失败，请检查统一物理模块")

if __name__ == "__main__":
    main()
